# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

---
apiVersion: gateway.networking.k8s.io/v1
kind: GatewayClass
metadata:
  name: envoy-ai-gateway-basic
spec:
  controllerName: gateway.envoyproxy.io/gatewayclass-controller
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: envoy-ai-gateway-basic-openai
  namespace: default
spec:
  endpoints:
    - fqdn:
        hostname: api.openai.com
        port: 443
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: envoy-ai-gateway-basic-aws
  namespace: default
spec:
  endpoints:
    - fqdn:
        hostname: bedrock-runtime.us-east-1.amazonaws.com
        port: 443
---
apiVersion: gateway.networking.k8s.io/v1alpha3
kind: BackendTLSPolicy
metadata:
  name: envoy-ai-gateway-basic-openai-tls
  namespace: default
spec:
  targetRefs:
    - group: gateway.envoyproxy.io
      kind: Backend
      name: envoy-ai-gateway-basic-openai
  validation:
    hostname: api.openai.com
    wellKnownCACertificates: System
---
apiVersion: gateway.networking.k8s.io/v1alpha3
kind: BackendTLSPolicy
metadata:
  name: envoy-ai-gateway-basic-aws-tls
  namespace: default
spec:
  targetRefs:
    - group: gateway.envoyproxy.io
      kind: Backend
      name: envoy-ai-gateway-basic-aws
  validation:
    hostname: bedrock-runtime.us-east-1.amazonaws.com
    wellKnownCACertificates: System
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: envoy-ai-gateway-basic
  namespace: default
  annotations:
    gateway.envoyproxy.io/backend-ref-priority: 0:envoy-ai-gateway-basic-openai:0,1:envoy-ai-gateway-basic-aws:0,2:envoy-ai-gateway-basic-testupstream:0
  ownerReferences:
    - apiVersion: aigateway.envoyproxy.io/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: AIGatewayRoute
      name: envoy-ai-gateway-basic
      uid: ""
spec:
  parentRefs:
    - name: envoy-ai-gateway-basic
      namespace: default
  rules:
    - backendRefs:
        - group: gateway.envoyproxy.io
          kind: Backend
          name: envoy-ai-gateway-basic-openai
      filters:
        - extensionRef:
            group: gateway.envoyproxy.io
            kind: HTTPRouteFilter
            name: ai-eg-host-rewrite
          type: ExtensionRef
      matches:
        - headers:
            - name: x-ai-eg-selected-route
              value: envoy-ai-gateway-basic-rule-0
      timeouts:
        request: 60s
    - backendRefs:
        - group: gateway.envoyproxy.io
          kind: Backend
          name: envoy-ai-gateway-basic-aws
      filters:
        - extensionRef:
            group: gateway.envoyproxy.io
            kind: HTTPRouteFilter
            name: ai-eg-host-rewrite
          type: ExtensionRef
      matches:
        - headers:
            - name: x-ai-eg-selected-route
              value: envoy-ai-gateway-basic-rule-1
      timeouts:
        request: 60s
    - backendRefs:
        - kind: Service
          name: envoy-ai-gateway-basic-testupstream
          port: 80
      filters:
        - extensionRef:
            group: gateway.envoyproxy.io
            kind: HTTPRouteFilter
            name: ai-eg-host-rewrite
          type: ExtensionRef
      matches:
        - headers:
            - name: x-ai-eg-selected-route
              value: envoy-ai-gateway-basic-rule-2
      timeouts:
        request: 60s
    - matches:
        - path:
            value: /
      name: unreachable
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyExtensionPolicy
metadata:
  name: ai-eg-eep-envoy-ai-gateway-basic
  namespace: default
spec:
  extProc:
    - backendRefs:
        - group: gateway.envoyproxy.io
          kind: Backend
          name: envoy-ai-gateway-extproc-backend
          namespace: default
      backendSettings:
        connection:
          bufferLimit: 50Mi
      metadata:
        writableNamespaces:
          - io.envoy.ai_gateway
      processingMode:
        allowModeOverride: true
        request:
          body: Buffered
        response:
          body: Buffered
  targetRefs:
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: envoy-ai-gateway-basic
status:
  ancestors: null
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: HTTPRouteFilter
metadata:
  name: ai-eg-host-rewrite
  namespace: default
spec:
  urlRewrite:
    hostname:
      type: Backend
---
apiVersion: v1
kind: Secret
metadata:
  name: envoy-ai-gateway-basic-default
  namespace: envoy-gateway-system
stringData:
  filter-config.yaml: |
    metadataNamespace: io.envoy.ai_gateway
    modelNameHeaderKey: x-ai-eg-model
    rules:
    - backends:
      - auth:
          apiKey:
            key: apiKey
        modelNameOverride: ""
        name: envoy-ai-gateway-basic-openai.default
        schema:
          name: OpenAI
          version: v1
      headers:
      - name: x-ai-eg-model
        value: gpt-4o-mini
      modelsCreatedAt: "2025-05-23T00:00:00Z"
      modelsOwnedBy: openai
      name: envoy-ai-gateway-basic-rule-0
    - backends:
      - auth:
          aws:
            credentialFileLiteral: |
              [default]
              aws_access_key_id = AWS_ACCESS_KEY_ID
              aws_secret_access_key = AWS_SECRET_ACCESS_KEY
            region: us-east-1
        modelNameOverride: us.meta.llama3-2-1b-instruct-v1:0
        name: envoy-ai-gateway-basic-aws.default
        schema:
          name: AWSBedrock
      headers:
      - name: x-ai-eg-model
        value: llama3-2-1b-instruct-v1
      modelsCreatedAt: "2025-05-23T00:00:00Z"
      modelsOwnedBy: aws
      name: envoy-ai-gateway-basic-rule-1
    - backends:
      - modelNameOverride: ""
        name: envoy-ai-gateway-basic-testupstream.default
        schema:
          name: OpenAI
          version: v1
      headers:
      - name: x-ai-eg-model
        value: some-cool-self-hosted-model
      modelsCreatedAt: "2025-05-23T00:00:00Z"
      modelsOwnedBy: Envoy AI Gateway
      name: envoy-ai-gateway-basic-rule-2
    schema:
      name: OpenAI
      version: v1
    selectedRouteHeaderKey: x-ai-eg-selected-route
    uuid: envoy-ai-gateway-basic
---
apiVersion: v1
kind: Secret
metadata:
  name: envoy-ai-gateway-basic-openai-apikey
  namespace: default
stringData:
  apiKey: apiKey
type: Opaque
---
apiVersion: v1
kind: Secret
metadata:
  name: envoy-ai-gateway-basic-aws-credentials
  namespace: default
stringData:
  credentials: |
    [default]
    aws_access_key_id = AWS_ACCESS_KEY_ID
    aws_secret_access_key = AWS_SECRET_ACCESS_KEY
type: Opaque
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: envoy-ai-gateway-basic
  namespace: default
spec:
  gatewayClassName: envoy-ai-gateway-basic
  listeners:
    - name: http
      port: 8888
      protocol: HTTP
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: envoy-ai-gateway-extproc-backend
  namespace: default
spec:
  endpoints:
    - unix:
        path: /var/run/translate.sock

