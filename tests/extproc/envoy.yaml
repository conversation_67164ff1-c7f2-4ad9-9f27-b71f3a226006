# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

admin:
  address:
    socket_address:
      address: 127.0.0.1
      port_value: 9901

static_resources:
  listeners:
    - address:
        socket_address:
          address: 0.0.0.0
          port_value: 1062
      filter_chains:
        - filters:
            - name: envoy.filters.network.http_connection_manager
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                stat_prefix: ingress_http
                codec_type: auto
                access_log:
                  - name: log_used_token
                    typed_config:
                      "@type": type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                      path: ACCESS_LOG_PATH
                      log_format:
                        json_format:
                          used_token: "%DYNAMIC_METADATA(ai_gateway_llm_ns:used_token)%"
                          some_cel: "%DYNAMIC_METADATA(ai_gateway_llm_ns:some_cel)%"
                route_config:
                  virtual_hosts:
                    - name: local_route
                      # TODO: move this at route level to match the behavior of EG.
                      retry_policy:
                        retry_on: "5xx,gateway-error,reset,rest-before-request,connect-failure,envoy-ratelimited,retriable-4xx,refused-stream,retriable-status-codes,retriable-headers"
                        num_retries: 5
                        per_try_timeout: "30s"
                        retry_back_off:
                          base_interval: "0.1s"
                          max_interval: "1s"
                      domains:
                        - "*"
                      routes:
                        - match:
                            prefix: "/"
                            headers:
                              - name: x-selected-route-name
                                string_match:
                                  exact: aws-bedrock-route
                          route:
                            auto_host_rewrite: true
                            cluster: aws_bedrock
                        - match:
                            prefix: "/"
                            headers:
                              - name: x-selected-route-name
                                string_match:
                                  exact: openai-route
                          route:
                            auto_host_rewrite: true
                            cluster: openai
                        - match:
                            prefix: "/"
                            headers:
                              - name: x-selected-route-name
                                string_match:
                                  exact: azure-openai-route
                          route:
                            auto_host_rewrite: true
                            cluster: azure_openai
                        - match:
                            prefix: "/"
                            headers:
                              - name: x-selected-route-name
                                string_match:
                                  exact: gemini-route
                          route:
                            auto_host_rewrite: true
                            cluster: gemini
                        - match:
                            prefix: "/"
                            headers:
                              - name: x-selected-route-name
                                string_match:
                                  exact: groq-route
                          route:
                            auto_host_rewrite: true
                            cluster: groq
                        - match:
                            prefix: "/"
                            headers:
                              - name: x-selected-route-name
                                string_match:
                                  exact: grok-route
                          route:
                            auto_host_rewrite: true
                            cluster: grok
                        - match:
                            prefix: "/"
                            headers:
                              - name: x-selected-route-name
                                string_match:
                                  exact: sambanova-route
                          route:
                            auto_host_rewrite: true
                            cluster: sambanova
                        - match:
                            prefix: "/"
                            headers:
                              - name: x-selected-route-name
                                string_match:
                                  exact: deepinfra-route
                          route:
                            auto_host_rewrite: true
                            cluster: deepinfra
                        - match:
                            prefix: "/"
                            headers:
                              - name: x-selected-route-name
                                string_match:
                                  exact: testupstream-openai-route
                          route:
                            cluster: testupstream-openai
                        - match:
                            prefix: "/"
                            headers:
                              - name: x-selected-route-name
                                string_match:
                                  exact: testupstream-modelname-override-route
                          route:
                            cluster: testupstream-modelname-override
                        - match:
                            prefix: "/"
                            headers:
                              - name: x-selected-route-name
                                string_match:
                                  exact: testupstream-aws-route
                          route:
                            cluster: testupstream-aws
                        - match:
                            prefix: "/"
                            headers:
                              - name: x-selected-route-name
                                string_match:
                                  exact: testupstream-azure-route
                          route:
                            cluster: testupstream-azure
                        - match:
                            prefix: "/"
                            headers:
                              - name: x-selected-route-name
                                string_match:
                                  exact: original_destination_cluster
                          name: original_destination_cluster
                          route:
                            cluster: original_destination_cluster
                http_filters:
                  - name: envoy.filters.http.ext_proc
                    typed_config:
                      "@type": type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
                      allow_mode_override: true
                      processing_mode:
                        request_header_mode: "SEND"
                        request_body_mode: "BUFFERED"
                        response_header_mode: "SEND"
                        response_body_mode: "BUFFERED"
                      grpc_service:
                        envoy_grpc:
                          cluster_name: extproc_cluster
                      metadataOptions:
                        receivingNamespaces:
                          untyped:
                            - ai_gateway_llm_ns
                  - name: envoy.filters.http.router
                    typed_config:
                      "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                      suppressEnvoyHeaders: true

  clusters:
    - name: extproc_cluster
      connect_timeout: 0.25s
      type: STATIC
      lb_policy: ROUND_ROBIN
      typed_extension_protocol_options:
        envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
          "@type": type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
          explicit_http_config:
            http2_protocol_options:
              connection_keepalive:
                interval: 30s
                timeout: 5s
      load_assignment:
        cluster_name: extproc_cluster
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 1063
    - name: testupstream-openai
      connect_timeout: 0.25s
      type: STATIC
      lb_policy: ROUND_ROBIN
      outlier_detection:
        consecutive_5xx: 1
        interval: 1s
        base_ejection_time: 2s  # Must be smaller than the require.Eventually's interval. Otherwise, the tests may pass without going through the fallback since the always-failing backend could be ejected by the time when require.Eventually retries due to the previous request IF the retry is not configured.
        max_ejection_percent: 100
      typed_extension_protocol_options:
        envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
          "@type": type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
          explicit_http_config:
            http_protocol_options: {}
          http_filters:
            - name: upstream_extproc
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
                allow_mode_override: true
                request_attributes:
                  - xds.upstream_host_metadata
                processing_mode:
                  request_header_mode: "SEND"
                  request_body_mode: "NONE"
                  response_header_mode: "SKIP"
                  response_body_mode: "NONE"
                grpc_service:
                  envoy_grpc:
                    cluster_name: extproc_cluster
                metadataOptions:
                  receivingNamespaces:
                    untyped:
                      - ai_gateway_llm_ns
            - name: envoy.filters.http.header_mutation
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.header_mutation.v3.HeaderMutation
                mutations:
                  request_mutations:
                    - append:
                        append_action: ADD_IF_ABSENT
                        header:
                          key: content-length
                          value: "%DYNAMIC_METADATA(ai_gateway_llm_ns:content_length)%"
            - name: envoy.filters.http.upstream_codec
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.upstream_codec.v3.UpstreamCodec
      load_assignment:
        cluster_name: testupstream-openai
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 8080
                metadata:
                  filter_metadata:
                    aigateway.envoy.io:
                      backend_name: "testupstream-openai"
    - name: testupstream-modelname-override
      connect_timeout: 0.25s
      type: STATIC
      lb_policy: ROUND_ROBIN
      outlier_detection:
        consecutive_5xx: 1
        interval: 1s
        base_ejection_time: 2s  # Must be smaller than the require.Eventually's interval. Otherwise, the tests may pass without going through the fallback since the always-failing backend could be ejected by the time when require.Eventually retries due to the previous request IF the retry is not configured.
        max_ejection_percent: 100
      typed_extension_protocol_options:
        envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
          "@type": type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
          explicit_http_config:
            http_protocol_options: {}
          http_filters:
            - name: upstream_extproc
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
                allow_mode_override: true
                request_attributes:
                  - xds.upstream_host_metadata
                processing_mode:
                  request_header_mode: "SEND"
                  request_body_mode: "NONE"
                  response_header_mode: "SKIP"
                  response_body_mode: "NONE"
                grpc_service:
                  envoy_grpc:
                    cluster_name: extproc_cluster
                metadataOptions:
                  receivingNamespaces:
                    untyped:
                      - ai_gateway_llm_ns
            - name: envoy.filters.http.header_mutation
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.header_mutation.v3.HeaderMutation
                mutations:
                  request_mutations:
                    - append:
                        append_action: ADD_IF_ABSENT
                        header:
                          key: content-length
                          value: "%DYNAMIC_METADATA(ai_gateway_llm_ns:content_length)%"
            - name: envoy.filters.http.upstream_codec
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.upstream_codec.v3.UpstreamCodec
      load_assignment:
        cluster_name: testupstream-modelname-override
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 8080
                metadata:
                  filter_metadata:
                    aigateway.envoy.io:
                      backend_name: "testupstream-modelname-override"
    - name: testupstream-aws
      connect_timeout: 0.25s
      type: STATIC
      lb_policy: ROUND_ROBIN
      outlier_detection:
        consecutive_5xx: 1
        interval: 1s
        base_ejection_time: 2s  # Must be smaller than the require.Eventually's interval. Otherwise, the tests may pass without going through the fallback since the always-failing backend could be ejected by the time when require.Eventually retries due to the previous request IF the retry is not configured.
        max_ejection_percent: 100
      typed_extension_protocol_options:
        envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
          "@type": type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
          explicit_http_config:
            http_protocol_options: {}
          http_filters:
            - name: upstream_extproc
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
                allow_mode_override: true
                request_attributes:
                  - xds.upstream_host_metadata
                processing_mode:
                  request_header_mode: "SEND"
                  request_body_mode: "NONE"
                  response_header_mode: "SKIP"
                  response_body_mode: "NONE"
                grpc_service:
                  envoy_grpc:
                    cluster_name: extproc_cluster
                metadataOptions:
                  receivingNamespaces:
                    untyped:
                      - ai_gateway_llm_ns
            - name: envoy.filters.http.header_mutation
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.header_mutation.v3.HeaderMutation
                mutations:
                  request_mutations:
                    - append:
                        append_action: ADD_IF_ABSENT
                        header:
                          key: content-length
                          value: "%DYNAMIC_METADATA(ai_gateway_llm_ns:content_length)%"
            - name: envoy.filters.http.upstream_codec
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.upstream_codec.v3.UpstreamCodec
      load_assignment:
        cluster_name: testupstream-aws
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 8080
                metadata:
                  filter_metadata:
                    aigateway.envoy.io:
                      backend_name: "testupstream-aws"
    - name: testupstream-azure
      connect_timeout: 0.25s
      type: STATIC
      lb_policy: ROUND_ROBIN
      outlier_detection:
        consecutive_5xx: 1
        interval: 1s
        base_ejection_time: 2s  # Must be smaller than the require.Eventually's interval. Otherwise, the tests may pass without going through the fallback since the always-failing backend could be ejected by the time when require.Eventually retries due to the previous request IF the retry is not configured.
        max_ejection_percent: 100
      typed_extension_protocol_options:
        envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
          "@type": type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
          explicit_http_config:
            http_protocol_options: {}
          http_filters:
            - name: upstream_extproc
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
                allow_mode_override: true
                request_attributes:
                  - xds.upstream_host_metadata
                processing_mode:
                  request_header_mode: "SEND"
                  request_body_mode: "NONE"
                  response_header_mode: "SKIP"
                  response_body_mode: "NONE"
                grpc_service:
                  envoy_grpc:
                    cluster_name: extproc_cluster
                metadataOptions:
                  receivingNamespaces:
                    untyped:
                      - ai_gateway_llm_ns
            - name: envoy.filters.http.header_mutation
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.header_mutation.v3.HeaderMutation
                mutations:
                  request_mutations:
                    - append:
                        append_action: ADD_IF_ABSENT
                        header:
                          key: content-length
                          value: "%DYNAMIC_METADATA(ai_gateway_llm_ns:content_length)%"
            - name: envoy.filters.http.upstream_codec
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.upstream_codec.v3.UpstreamCodec
      load_assignment:
        cluster_name: testupstream-azure
        endpoints:
          - priority: 0  # Primary.
            lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 1066
                metadata:
                  filter_metadata:
                    aigateway.envoy.io:
                      backend_name: "always-failing-backend"
          - priority: 1  # Secondary.
            lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 8080
                metadata:
                  filter_metadata:
                    aigateway.envoy.io:
                      backend_name: "testupstream-azure"
    - name: aws_bedrock
      connect_timeout: 30s
      type: STRICT_DNS
      outlier_detection:
        consecutive_5xx: 1
        interval: 1s
        base_ejection_time: 2s  # Must be smaller than the require.Eventually's interval. Otherwise, the tests may pass without going through the fallback since the always-failing backend could be ejected by the time when require.Eventually retries due to the previous request IF the retry is not configured.
        max_ejection_percent: 100
      typed_extension_protocol_options:
        envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
          "@type": type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
          explicit_http_config:
            http_protocol_options: {}
          http_filters:
            - name: upstream_extproc
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
                allow_mode_override: true
                request_attributes:
                  - xds.upstream_host_metadata
                processing_mode:
                  request_header_mode: "SEND"
                  request_body_mode: "NONE"
                  response_header_mode: "SKIP"
                  response_body_mode: "NONE"
                grpc_service:
                  envoy_grpc:
                    cluster_name: extproc_cluster
                metadataOptions:
                  receivingNamespaces:
                    untyped:
                      - ai_gateway_llm_ns
            - name: envoy.filters.http.header_mutation
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.header_mutation.v3.HeaderMutation
                mutations:
                  request_mutations:
                    - append:
                        append_action: ADD_IF_ABSENT
                        header:
                          key: content-length
                          value: "%DYNAMIC_METADATA(ai_gateway_llm_ns:content_length)%"
            - name: envoy.filters.http.upstream_codec
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.upstream_codec.v3.UpstreamCodec
      load_assignment:
        cluster_name: aws_bedrock
        endpoints:
          - priority: 0  # Primary.
            lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 1067  # Speaks TLS.
                metadata:
                  filter_metadata:
                    aigateway.envoy.io:
                      backend_name: "always-failing-backend"
          - priority: 1  # Secondary.
            lb_endpoints:
              - endpoint:
                  hostname: bedrock-runtime.us-east-1.amazonaws.com
                  address:
                    socket_address:
                      address: bedrock-runtime.us-east-1.amazonaws.com
                      port_value: 443
                metadata:
                  filter_metadata:
                    aigateway.envoy.io:
                      backend_name: "aws-bedrock"
      transport_socket:
        name: envoy.transport_sockets.tls
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
          auto_host_sni: true
    - name: openai
      connect_timeout: 30s
      type: STRICT_DNS
      outlier_detection:
        consecutive_5xx: 1
        interval: 1s
        base_ejection_time: 2s  # Must be smaller than the require.Eventually's interval. Otherwise, the tests may pass without going through the fallback since the always-failing backend could be ejected by the time when require.Eventually retries due to the previous request IF the retry is not configured.
        max_ejection_percent: 100
      typed_extension_protocol_options:
        envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
          "@type": type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
          explicit_http_config:
            http_protocol_options: {}
          http_filters:
            - name: upstream_extproc
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
                allow_mode_override: true
                request_attributes:
                  - xds.upstream_host_metadata
                processing_mode:
                  request_header_mode: "SEND"
                  request_body_mode: "NONE"
                  response_header_mode: "SKIP"
                  response_body_mode: "NONE"
                grpc_service:
                  envoy_grpc:
                    cluster_name: extproc_cluster
                metadataOptions:
                  receivingNamespaces:
                    untyped:
                      - ai_gateway_llm_ns
            - name: envoy.filters.http.header_mutation
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.header_mutation.v3.HeaderMutation
                mutations:
                  request_mutations:
                    - append:
                        append_action: ADD_IF_ABSENT
                        header:
                          key: content-length
                          value: "%DYNAMIC_METADATA(ai_gateway_llm_ns:content_length)%"
            - name: envoy.filters.http.upstream_codec
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.upstream_codec.v3.UpstreamCodec
      load_assignment:
        cluster_name: openai
        endpoints:
          - priority: 0  # Primary.
            lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 1067  # Speaks TLS.
                metadata:
                  filter_metadata:
                    aigateway.envoy.io:
                      backend_name: "always-failing-backend"
          - priority: 1  # Secondary.
            lb_endpoints:
              - endpoint:
                  hostname: api.openai.com
                  address:
                    socket_address:
                      address: api.openai.com
                      port_value: 443
                metadata:
                  filter_metadata:
                    aigateway.envoy.io:
                      backend_name: "openai"
      transport_socket:
        name: envoy.transport_sockets.tls
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
          auto_host_sni: true
    - name: azure_openai
      connect_timeout: 30s
      type: STRICT_DNS
      outlier_detection:
        consecutive_5xx: 1
        interval: 1s
        base_ejection_time: 2s  # Must be smaller than the require.Eventually's interval. Otherwise, the tests may pass without going through the fallback since the always-failing backend could be ejected by the time when require.Eventually retries due to the previous request IF the retry is not configured.
        max_ejection_percent: 100
      typed_extension_protocol_options:
        envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
          "@type": type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
          explicit_http_config:
            http_protocol_options: {}
          http_filters:
            - name: upstream_extproc
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
                allow_mode_override: true
                request_attributes:
                  - xds.upstream_host_metadata
                processing_mode:
                  request_header_mode: "SEND"
                  request_body_mode: "NONE"
                  response_header_mode: "SKIP"
                  response_body_mode: "NONE"
                grpc_service:
                  envoy_grpc:
                    cluster_name: extproc_cluster
                metadataOptions:
                  receivingNamespaces:
                    untyped:
                      - ai_gateway_llm_ns
            - name: envoy.filters.http.header_mutation
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.header_mutation.v3.HeaderMutation
                mutations:
                  request_mutations:
                    - append:
                        append_action: ADD_IF_ABSENT
                        header:
                          key: content-length
                          value: "%DYNAMIC_METADATA(ai_gateway_llm_ns:content_length)%"
            - name: envoy.filters.http.upstream_codec
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.upstream_codec.v3.UpstreamCodec
      load_assignment:
        cluster_name: azure_openai
        endpoints:
          - priority: 0  # Primary.
            lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 1067  # Speaks TLS.
                metadata:
                  filter_metadata:
                    aigateway.envoy.io:
                      backend_name: "always-failing-backend"
          - priority: 1  # Secondary.
            lb_endpoints:
              - endpoint:
                  hostname: <azure_resource_name>.openai.azure.com   # Replace with your azure resource name
                  address:
                    socket_address:
                      address: <azure_resource_name>.openai.azure.com   # Replace with your azure resource name
                      port_value: 443
                metadata:
                  filter_metadata:
                    aigateway.envoy.io:
                      backend_name: "azure-openai"
      transport_socket:
        name: envoy.transport_sockets.tls
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
          auto_host_sni: true
    - name: original_destination_cluster
      connectTimeout: 30s
      type: ORIGINAL_DST
      lbPolicy: CLUSTER_PROVIDED
      originalDstLbConfig:
        httpHeaderName: x-ai-eg-original-dst
        useHttpHeader: true
    - name: gemini
      connect_timeout: 30s
      type: STRICT_DNS
      dns_lookup_family: V4_ONLY
      outlier_detection:
        consecutive_5xx: 1
        interval: 1s
        base_ejection_time: 2s  # Must be smaller than the require.Eventually's interval. Otherwise, the tests may pass without going through the fallback since the always-failing backend could be ejected by the time when require.Eventually retries due to the previous request IF the retry is not configured.
        max_ejection_percent: 100
      typed_extension_protocol_options:
        envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
          "@type": type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
          explicit_http_config:
            http_protocol_options: {}
          http_filters:
            - name: upstream_extproc
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
                allow_mode_override: true
                request_attributes:
                  - xds.upstream_host_metadata
                processing_mode:
                  request_header_mode: "SEND"
                  request_body_mode: "NONE"
                  response_header_mode: "SKIP"
                  response_body_mode: "NONE"
                grpc_service:
                  envoy_grpc:
                    cluster_name: extproc_cluster
                metadataOptions:
                  receivingNamespaces:
                    untyped:
                      - ai_gateway_llm_ns
            - name: envoy.filters.http.header_mutation
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.header_mutation.v3.HeaderMutation
                mutations:
                  request_mutations:
                    - append:
                        append_action: ADD_IF_ABSENT
                        header:
                          key: content-length
                          value: "%DYNAMIC_METADATA(ai_gateway_llm_ns:content_length)%"
            - name: envoy.filters.http.upstream_codec
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.upstream_codec.v3.UpstreamCodec
      load_assignment:
        cluster_name: gemini
        endpoints:
          - lb_endpoints:
              - endpoint:
                  hostname: generativelanguage.googleapis.com
                  address:
                    socket_address:
                      address: generativelanguage.googleapis.com
                      port_value: 443
                metadata:
                  filter_metadata:
                    aigateway.envoy.io:
                      backend_name: "gemini"
      transport_socket:
        name: envoy.transport_sockets.tls
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
          auto_host_sni: true
    - name: groq
      connect_timeout: 30s
      type: STRICT_DNS
      dns_lookup_family: V4_ONLY
      outlier_detection:
        consecutive_5xx: 1
        interval: 1s
        base_ejection_time: 2s  # Must be smaller than the require.Eventually's interval. Otherwise, the tests may pass without going through the fallback since the always-failing backend could be ejected by the time when require.Eventually retries due to the previous request IF the retry is not configured.
        max_ejection_percent: 100
      typed_extension_protocol_options:
        envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
          "@type": type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
          explicit_http_config:
            http_protocol_options: {}
          http_filters:
            - name: upstream_extproc
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
                allow_mode_override: true
                request_attributes:
                  - xds.upstream_host_metadata
                processing_mode:
                  request_header_mode: "SEND"
                  request_body_mode: "NONE"
                  response_header_mode: "SKIP"
                  response_body_mode: "NONE"
                grpc_service:
                  envoy_grpc:
                    cluster_name: extproc_cluster
                metadataOptions:
                  receivingNamespaces:
                    untyped:
                      - ai_gateway_llm_ns
            - name: envoy.filters.http.header_mutation
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.header_mutation.v3.HeaderMutation
                mutations:
                  request_mutations:
                    - append:
                        append_action: ADD_IF_ABSENT
                        header:
                          key: content-length
                          value: "%DYNAMIC_METADATA(ai_gateway_llm_ns:content_length)%"
            - name: envoy.filters.http.upstream_codec
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.upstream_codec.v3.UpstreamCodec
      load_assignment:
        cluster_name: groq
        endpoints:
          - lb_endpoints:
              - endpoint:
                  hostname: api.groq.com
                  address:
                    socket_address:
                      address: api.groq.com
                      port_value: 443
                metadata:
                  filter_metadata:
                    aigateway.envoy.io:
                      backend_name: "groq"
      transport_socket:
        name: envoy.transport_sockets.tls
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
          auto_host_sni: true
    - name: grok
      connect_timeout: 30s
      type: STRICT_DNS
      dns_lookup_family: V4_ONLY
      outlier_detection:
        consecutive_5xx: 1
        interval: 1s
        base_ejection_time: 2s  # Must be smaller than the require.Eventually's interval. Otherwise, the tests may pass without going through the fallback since the always-failing backend could be ejected by the time when require.Eventually retries due to the previous request IF the retry is not configured.
        max_ejection_percent: 100
      typed_extension_protocol_options:
        envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
          "@type": type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
          explicit_http_config:
            http_protocol_options: {}
          http_filters:
            - name: upstream_extproc
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
                allow_mode_override: true
                request_attributes:
                  - xds.upstream_host_metadata
                processing_mode:
                  request_header_mode: "SEND"
                  request_body_mode: "NONE"
                  response_header_mode: "SKIP"
                  response_body_mode: "NONE"
                grpc_service:
                  envoy_grpc:
                    cluster_name: extproc_cluster
                metadataOptions:
                  receivingNamespaces:
                    untyped:
                      - ai_gateway_llm_ns
            - name: envoy.filters.http.header_mutation
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.header_mutation.v3.HeaderMutation
                mutations:
                  request_mutations:
                    - append:
                        append_action: ADD_IF_ABSENT
                        header:
                          key: content-length
                          value: "%DYNAMIC_METADATA(ai_gateway_llm_ns:content_length)%"
            - name: envoy.filters.http.upstream_codec
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.upstream_codec.v3.UpstreamCodec
      load_assignment:
        cluster_name: grok
        endpoints:
          - lb_endpoints:
              - endpoint:
                  hostname: api.x.ai
                  address:
                    socket_address:
                      address: api.x.ai
                      port_value: 443
                metadata:
                  filter_metadata:
                    aigateway.envoy.io:
                      backend_name: "grok"
      transport_socket:
        name: envoy.transport_sockets.tls
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
          auto_host_sni: true
    - name: sambanova
      connect_timeout: 30s
      type: STRICT_DNS
      dns_lookup_family: V4_ONLY
      outlier_detection:
        consecutive_5xx: 1
        interval: 1s
        base_ejection_time: 2s  # Must be smaller than the require.Eventually's interval. Otherwise, the tests may pass without going through the fallback since the always-failing backend could be ejected by the time when require.Eventually retries due to the previous request IF the retry is not configured.
        max_ejection_percent: 100
      typed_extension_protocol_options:
        envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
          "@type": type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
          explicit_http_config:
            http_protocol_options: {}
          http_filters:
            - name: upstream_extproc
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
                allow_mode_override: true
                request_attributes:
                  - xds.upstream_host_metadata
                processing_mode:
                  request_header_mode: "SEND"
                  request_body_mode: "NONE"
                  response_header_mode: "SKIP"
                  response_body_mode: "NONE"
                grpc_service:
                  envoy_grpc:
                    cluster_name: extproc_cluster
                metadataOptions:
                  receivingNamespaces:
                    untyped:
                      - ai_gateway_llm_ns
            - name: envoy.filters.http.header_mutation
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.header_mutation.v3.HeaderMutation
                mutations:
                  request_mutations:
                    - append:
                        append_action: ADD_IF_ABSENT
                        header:
                          key: content-length
                          value: "%DYNAMIC_METADATA(ai_gateway_llm_ns:content_length)%"
            - name: envoy.filters.http.upstream_codec
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.upstream_codec.v3.UpstreamCodec
      load_assignment:
        cluster_name: sambanova
        endpoints:
          - lb_endpoints:
              - endpoint:
                  hostname: api.sambanova.ai
                  address:
                    socket_address:
                      address: api.sambanova.ai
                      port_value: 443
                metadata:
                  filter_metadata:
                    aigateway.envoy.io:
                      backend_name: "sambanova"
      transport_socket:
        name: envoy.transport_sockets.tls
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
          auto_host_sni: true
    - name: deepinfra
      connect_timeout: 30s
      type: STRICT_DNS
      dns_lookup_family: V4_ONLY
      outlier_detection:
        consecutive_5xx: 1
        interval: 1s
        base_ejection_time: 2s  # Must be smaller than the require.Eventually's interval. Otherwise, the tests may pass without going through the fallback since the always-failing backend could be ejected by the time when require.Eventually retries due to the previous request IF the retry is not configured.
        max_ejection_percent: 100
      typed_extension_protocol_options:
        envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
          "@type": type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
          explicit_http_config:
            http_protocol_options: {}
          http_filters:
            - name: upstream_extproc
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
                allow_mode_override: true
                request_attributes:
                  - xds.upstream_host_metadata
                processing_mode:
                  request_header_mode: "SEND"
                  request_body_mode: "NONE"
                  response_header_mode: "SKIP"
                  response_body_mode: "NONE"
                grpc_service:
                  envoy_grpc:
                    cluster_name: extproc_cluster
                metadataOptions:
                  receivingNamespaces:
                    untyped:
                      - ai_gateway_llm_ns
            - name: envoy.filters.http.header_mutation
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.header_mutation.v3.HeaderMutation
                mutations:
                  request_mutations:
                    - append:
                        append_action: ADD_IF_ABSENT
                        header:
                          key: content-length
                          value: "%DYNAMIC_METADATA(ai_gateway_llm_ns:content_length)%"
            - name: envoy.filters.http.upstream_codec
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.http.upstream_codec.v3.UpstreamCodec
      load_assignment:
        cluster_name: deepinfra
        endpoints:
          - lb_endpoints:
              - endpoint:
                  hostname: api.deepinfra.com
                  address:
                    socket_address:
                      address: api.deepinfra.com
                      port_value: 443
                metadata:
                  filter_metadata:
                    aigateway.envoy.io:
                      backend_name: "deepinfra"
      transport_socket:
        name: envoy.transport_sockets.tls
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
          auto_host_sni: true

overload_manager:
  refresh_interval: 0.25s
  resource_monitors:
    - name: "envoy.resource_monitors.global_downstream_max_connections"
      typed_config:
        "@type": type.googleapis.com/envoy.extensions.resource_monitors.downstream_connections.v3.DownstreamConnectionsConfig
        max_active_downstream_connections: 1000
